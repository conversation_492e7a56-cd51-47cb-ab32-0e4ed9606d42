import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { ACCESS_COOKIE } from "./constants/api.consants";
import { ROUTES } from "./constants/routes.constants";

const PROTECTED = ["/protected"];

export function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  const needsAuth = PROTECTED.some((p) => pathname.startsWith(p));
  if (!needsAuth) return;

  const access = req.cookies.get(ACCESS_COOKIE);
  if (!access) {
    const login = new URL(ROUTES.SIGN_IN, req.url);
    login.searchParams.set("next", pathname);
    return NextResponse.redirect(login);
  }
}

export const config = {
  matcher: ["/:path*"],
};
