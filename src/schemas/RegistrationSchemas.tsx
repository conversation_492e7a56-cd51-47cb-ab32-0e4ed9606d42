// schemas.ts
import { z } from "zod";

export const personalInfoSchema = z.object({
  full_name: z.string().min(1, "Full name is required"),
  // email: z.string().email("Invalid email address"),
  date_of_birth: z.string().min(1, "Date of birth is required"),
  cnic_number: z.string().optional(),
  city: z.string().min(1, "City is required"),
});

export const educationSchema = z.object({
  basic_education: z.string().min(1, "Basic education is required"),
  last_degree: z.string().min(1, "Degree is required"),
  field_of_study: z.string().min(1, "Field of study is required"),
  institution_name: z.string().min(1, "Institution name is required"),
});

export const motivationSchema = z.object({
  motivation: z.string().min(1, "Reason is required"),
  learning_expectations: z.string().min(1, "Course expectations are required"),
  has_taken_similar_courses: z
    .string()
    .refine((val) => val === "yes" || val === "no", {
      message: "Please select if you have studied similar courses before",
    }),
  agree_terms: z.boolean().refine((val) => val, {
    message: "You must agree to the terms and conditions",
  }),
});

// Final combined schema
export const fullFormSchema = personalInfoSchema
  .merge(educationSchema)
  .merge(motivationSchema);
