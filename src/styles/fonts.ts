import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>_Mono,
  <PERSON><PERSON>_Serif,
  Plus_Jakarta_Sans,
  Inter,
  Poppins,
} from "next/font/google";

export const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

export const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const notoSerif = Noto_Serif({
  variable: "--font-noto",
  subsets: ["latin"],
});

export const plusJakartaSans = Plus_Jakarta_Sans({
  variable: "--",
  subsets: ["latin"],
});

export const fontInter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const fontPoppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["400", "700"],
});
