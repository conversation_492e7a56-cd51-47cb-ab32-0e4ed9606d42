@import url('https://fonts.googleapis.com/css2?family=Geist:wght@100..900&family=Inter:wght@100..900&family=Plus+Jakarta+Sans:wght@100..900&family=Noto+Serif:ital,wght@0,100..900;1,100..900&display=swap');


/* globals.css or styles/globals.css */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global scroll behavior */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  scroll-behavior: smooth;
}

/* Apply default font to body instead */
body {
  font-family: 'Plus Jakarta Sans', sans-serif;
}


a {
  text-decoration: none;
  color: inherit;
}

ul,
ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

input,
textarea,
select {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px;
  font-size: 16px;
  width: 100%;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}
