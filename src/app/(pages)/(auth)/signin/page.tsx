"use client";

import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { getValidAccessToken, login } from "@/services/auth";
import ThemedButton from "@/components/common/ThemedButton";
import toast from "react-hot-toast"; // or any toaster lib
import { useAuth } from "@/providers/AuthProvider";
import { jwtDecode } from "jwt-decode";
import SocialButton from "@/components/common/SocialButton";
import { JwtPayloadX } from "@/types/auth.types";
import { ROUTES } from "@/constants/routes.constants";
import { useDarkMode } from "@/providers/DarkModeProvider";
import FormField from "@/components/common/FormField";

const schema = z.object({
  email: z.string().email({ message: "Invalid e-mail" }),
  password: z.string().min(8, "Min 8 characters"),
});
type FormData = z.infer<typeof schema>;

export default function SignIn() {
  const router = useRouter();
  const { updateUser } = useAuth();
  const { isDarkMode } = useDarkMode();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
  });

  async function onSubmit(data: FormData) {
    try {
      await login(data.email, data.password);
      const token = await getValidAccessToken();
      const decodedToken: JwtPayloadX | null = token ? jwtDecode(token) : null;

      updateUser(decodedToken);
      toast.success(
        "Welcome back! " +
          ((decodedToken?.full_name ?? "")?.length > 0
            ? `${decodedToken?.full_name}`
            : "")
      );

      if (decodedToken?.is_registration_complete) {
        router.replace("/");
      } else {
        router.replace(ROUTES.REGISTRATION);
      }
    } catch (err) {
      const msg =
        err instanceof Error ? err.message : "Login failed. Try again.";
      toast.error(msg);
    }
  }

  console.log(isDarkMode);

  return (
    // <div className="grid min-h-screen grid-cols-1 lg:grid-cols-2">
    <div
      className={`grid min-h-screen grid-cols-1 lg:grid-cols-2 ${isDarkMode ? "dark" : ""}`}
    >
      {/* Left side with image */}
      <aside className="hidden flex-col bg-theme_primary lg:flex">
        <div className="px-8 py-8 text-white">
          <h2 className=" font-semibold  lg:text-3xl xl:text-4xl">
            Rahnaward
            <br />
            Academy
          </h2>
        </div>

        <div className="relative flex flex-1 items-center justify-center overflow-hidden">
          <Image
            src="/vectors/vector_hero.png"
            alt=""
            fill
            priority
            className="object-contain"
          />

          <Image
            src="/assets/auth/signin_hero.png"
            alt="Students high-fiving"
            fill
            priority
            className="relative object-contain shadow-xl p-[11dvh]"
          />
        </div>
      </aside>

      <main className="flex flex-col w-full bg-[#e0e1dd] px-6 py-10 sm:px-12 xl:px-20 dark:bg-dark_secondary">
        <Link
          href="/"
          className="font-inter font-semibold mb-6 flex items-center gap-2 text-sm text-[#1B263B] dark:text-white/70"
        >
          <ArrowLeft size={18} />
          Back
        </Link>

        <div className="flex flex-col w-full h-full items-center justify-center px-[7dvw]">
          <h1 className="text-center  text-3xl font-semibold text-theme_primary dark:text-white">
            Welcome
          </h1>
          <p className="mb-10 mt-2 text-center font-inter font-semibold text-sm text-[#1B263B] dark:text-white/70">
            Login to Rahnaward Academy
          </p>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 w-full">
            <FormField
              type="email"
              placeholder="Enter Email Address*"
              register={register}
              name="email"
              error={errors.email}
            />

            <FormField
              type="password"
              placeholder="Enter Password*"
              register={register}
              name="password"
              error={errors.password}
            />

            <div className="text-left">
              <Link
                href="#"
                className="text-sm text-theme_primary font-semibold font-inter dark:text-white/70"
              >
                Forgot Password?
              </Link>
            </div>

            <ThemedButton
              type="submit"
              disabled={isSubmitting}
              addClassName="w-full rounded-md !text-base font-medium tracking-wider disabled:opacity-60 border-none"
            >
              {isSubmitting ? "Signing in…" : "Sign In"}
            </ThemedButton>
          </form>

          <p className="mt-4 text-center text-sm text-[#1B263B] dark:text-white/70">
            <span className="font-inter">Don’t have an account?</span>

            <Link
              href="/signup"
              className="ml-5  font-semibold text-[#091B33] text-lg dark:text-theme_secondary"
            >
              Sign Up
            </Link>
          </p>

          <div className="my-8 flex items-center gap-4 w-full">
            <div className="h-px bg-[#1B263B]/20  w-full dark:bg-white/20" />
            <span className="font-inter dark:text-white/70">or</span>
            <div className="h-px bg-[#1B263B]/20  w-full dark:bg-white/20" />
          </div>

          <div className="space-y-4 w-full">
            <SocialButton provider="Google" iconSrc="/icons/google.png" />
            {/* <SocialButton provider="Apple" iconSrc="/icons/apple.png" />
            <SocialButton
              provider="Facebook"
              iconSrc="/icons/facebook.png"
            /> */}
          </div>
        </div>
      </main>
    </div>
  );
}
