"use client";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import RegistrationHeader from "@/components/registration/RegistrationHeader";
import { z } from "zod";
import { useEffect, useState } from "react";
import { fullFormSchema } from "@/schemas/RegistrationSchemas";
import Image from "next/image";
import RegistrationForm from "@/components/registration/RegistrationForm";
import { saveRegistrationDetails } from "@/services/user";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/constants/routes.constants";
import { useAuth } from "@/providers/AuthProvider";

const fullSchema = fullFormSchema;

export type RegistrationFormData = z.infer<typeof fullSchema>;

const Page = () => {
  /* ---------------------------------- HOOKS --------------------------------- */
  const [currentStep, setCurrentStep] = useState<number>(1);
  const { user, updateUser } = useAuth();

  const router = useRouter();

  const methods = useForm<RegistrationFormData>({
    defaultValues: {},
    mode: "onTouched",
    resolver: zodResolver(fullSchema),
  });

  /* --------------------------------- EFFECTS -------------------------------- */

  useEffect(() => {
    if (!user) {
      router.replace(ROUTES.SIGN_IN);
      return;
    } else if (user?.is_registration_complete) {
      router.replace(ROUTES.HOME);
    }
  }, [user, router]);

  /* --------------------------------- HELPERS -------------------------------- */

  const handleNext = async () => {
    let isStepValid = false;

    switch (currentStep) {
      case 1:
        isStepValid = await methods.trigger([
          "full_name",
          "date_of_birth",
          "city",
        ]);
        break;
      case 2:
        isStepValid = await methods.trigger([
          "basic_education",
          "last_degree",
          "field_of_study",
          "institution_name",
        ]);
        break;
      case 3:
        isStepValid = await methods.trigger([
          "motivation",
          "learning_expectations",
          "has_taken_similar_courses",
          "agree_terms",
        ]);
        break;
    }

    if (isStepValid) {
      if (currentStep === 3) {
        await methods.handleSubmit(onSubmit)();
      } else {
        setCurrentStep((prev) => prev + 1);
      }
    }
  };

  async function onSubmit(data: RegistrationFormData) {
    try {
      await saveRegistrationDetails(data);
      toast.success("Registration details saved successfully!");
      if (user) {
        updateUser({
          ...user,
          is_registration_complete: true,
          full_name: data.full_name,
        });
      }
      router.replace(ROUTES.HOME);
    } catch (err) {
      const msg =
        err instanceof Error ? err.message : "Login failed. Try again.";
      toast.error(msg);
    }
  }

  /* ----------------------------------- UI ----------------------------------- */

  return (
    <div className="bg-[#e0e1dd] min-h-screen pb-5 dark:bg-dark_secondary">
      <Image
        src="/assets/auth/grass_top.png"
        alt="Grass Top"
        className="absolute top-0 right-0 "
        width={300}
        height={200}
      />
      <Image
        src="/assets/auth/grass_bottom.png"
        alt="Grass Bottom"
        className="absolute bottom-0 left-0 "
        width={200}
        height={200}
      />
      <Image
        src="/assets/auth/circles.png"
        alt="Circles"
        className="absolute bottom-0 left-1/2 -translate-x-1/2 h-auto dark:hidden"
        width={1150}
        height={100}
      />

      <div className="relative z-0">
        <RegistrationHeader
          currentStep={currentStep}
          setCurrentStep={setCurrentStep}
          onNext={handleNext}
        />

        <RegistrationForm
          currentStep={currentStep}
          setCurrentStep={setCurrentStep}
          methods={methods}
          handleNext={handleNext}
          isSubmitting={methods.formState.isSubmitting}
        />
      </div>
    </div>
  );
};

export default Page;
