"use client";

import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import toast from "react-hot-toast";
import { jwtDecode } from "jwt-decode";

import { signup, login, getValidAccessToken } from "@/services/auth";
import { useAuth } from "@/providers/AuthProvider";
import ThemedButton from "@/components/common/ThemedButton";
import { JwtPayloadX } from "@/types/auth.types";
import SocialButton from "@/components/common/SocialButton";
import { ROUTES } from "@/constants/routes.constants";
import { useDarkMode } from "@/providers/DarkModeProvider";
import FormField from "@/components/common/FormField";

const schema = z.object({
  email: z.string().email({ message: "Invalid e-mail" }),
  password: z.string().min(8, "Password must be at least 8 characters"),
});
type FormData = z.infer<typeof schema>;

export default function SignUp() {
  const router = useRouter();
  const { updateUser } = useAuth();
  const { isDarkMode } = useDarkMode();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
  });

  const onSubmit = async (data: FormData) => {
    try {
      await signup(data.email, data.password);
      await login(data.email, data.password);
      const token = await getValidAccessToken();
      const decodedToken: JwtPayloadX | null = token ? jwtDecode(token) : null;

      updateUser(decodedToken);
      toast.success("Account created! Welcome 🎉");

      if (decodedToken?.is_registration_complete) {
        router.replace("/");
      } else {
        router.replace(ROUTES.REGISTRATION);
      }
    } catch (err) {
      toast.error(
        err instanceof Error ? err.message : "Sign-up failed. Try again."
      );
    }
  };

  return (
    <div
      className={`grid min-h-screen grid-cols-1 lg:grid-cols-2 ${isDarkMode ? "dark" : ""}`}
    >
      <aside className="hidden flex-col bg-theme_primary lg:flex">
        <div className="px-8 py-8 text-white">
          <h2 className=" font-semibold lg:text-3xl xl:text-4xl">
            Rahnaward
            <br />
            Academy
          </h2>
        </div>

        <div className="relative flex flex-1 items-center justify-center overflow-hidden">
          <Image
            src="/vectors/vector_hero.png"
            alt=""
            fill
            priority
            className="object-contain"
          />
          <Image
            src="/assets/auth/signup_hero.png"
            alt="Students high-fiving"
            fill
            priority
            className="relative object-contain shadow-xl p-[11dvh]"
          />
        </div>
      </aside>

      <main className="flex flex-col w-full bg-[#e0e1dd] px-6 py-10 sm:px-12 xl:px-20 dark:bg-dark_secondary">
        <Link
          href="/"
          className="mb-6 flex items-center gap-2 text-sm font-inter dark:text-white/70"
        >
          <ArrowLeft size={18} /> Back
        </Link>

        <div className="flex grow flex-col items-center justify-center px-[7dvw]">
          <h2 className="text-center  text-xl font-semibold text-[#0D1B2A] dark:text-white">
            Build resilience, think strategically,
            <br />
            speak powerfully all in one place.
          </h2>
          <p className="mt-2 mb-10 text-center font-inter text-[#1B263B] dark:text-white/70">
            Join for free. Learn for life.
          </p>

          <SocialBlock />

          <div className="my-6 flex w-full items-center gap-4">
            <div className="h-px w-full bg-[#1B263B]/20 dark:bg-white/20" />
            <span className="text-sm font-inter dark:text-white/70">or</span>
            <div className="h-px w-full bg-[#1B263B]/20 dark:bg-white/20" />
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 w-full">
            <FormField
              type="email"
              placeholder="Email Address*"
              register={register}
              name="email"
              error={errors.email}
            />

            <FormField
              type="password"
              placeholder="Enter Password*"
              register={register}
              name="password"
              error={errors.password}
            />

            <ThemedButton
              type="submit"
              disabled={isSubmitting}
              addClassName="w-full rounded-md !text-base border-none !mt-5 font-semibold disabled:opacity-60"
            >
              {isSubmitting ? "Creating account…" : "Sign Up"}
            </ThemedButton>
          </form>

          <p className="my-6 font-inter text-sm text-[#1B263B] dark:text-white/70">
            Already a member?{" "}
            <Link
              href={ROUTES.SIGN_IN}
              className="ml-5 text-lg font-semibold cursor-pointer text-[#1B263B] dark:text-theme_secondary"
            >
              Sign in
            </Link>
          </p>

          <div className="h-px w-full bg-[#1B263B]/20" />

          <p className="font-inter mt-8 text-center text-sm text-[#1B263B] dark:text-white/70">
            By signing up you agree to Rahnaward Academy’s{" "}
            <Link href={ROUTES.TERMS_AND_CONDITIONS} className=" font-semibold">
              Terms of Services
            </Link>{" "}
            {/* and <span className="font-semibold">Privacy Policy</span>. */}
            and{" "}
            <Link href={ROUTES.PRIVACY_POLICY} className=" font-semibold">
              Privacy Policy
            </Link>
            .
          </p>
        </div>
      </main>
    </div>
  );
}

function SocialBlock() {
  return (
    <div className="space-y-4 w-full">
      <SocialButton provider="Google" iconSrc="/icons/google.png" />
      {/* <SocialButton provider="Apple" iconSrc="/icons/apple.png" />
      <SocialButton provider="Facebook" iconSrc="/icons/facebook.png" /> */}
    </div>
  );
}
