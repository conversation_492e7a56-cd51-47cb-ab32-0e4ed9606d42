"use client";

import { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import SectionX from "@/components/layout/SectionX";
import ThemedButton from "@/components/common/ThemedButton";
import FormField from "@/components/common/FormField";

const partnerLogos = [
  "/logos/ibm_logo.png",
  "/logos/ibm_logo.png",
  "/logos/ibm_logo.png",
  "/logos/ibm_logo.png",
];

const news = Array.from({ length: 3 }).map((_, idx) => ({
  id: idx,
  title: "Upskilling Future Educators",
  img: "/assets/partners/hero_2.png",
  desc: "We collaborate with EduBridge to train teachers across Pakistan in practical AI tools and personalized learning systems.",
}));

function PartnerModal({ onClose }: { onClose: () => void }) {
  const handleEsc = useCallback(
    (e: KeyboardEvent) => e.key === "Escape" && onClose(),
    [onClose]
  );

  useEffect(() => {
    document.addEventListener("keydown", handleEsc);
    document.body.style.overflow = "hidden"; // freeze page scroll
    return () => {
      document.removeEventListener("keydown", handleEsc);
      document.body.style.overflow = "";
    };
  }, [handleEsc]);

  return (
    <div
      className="fixed inset-0 z-[999] flex items-center justify-center bg-black/50 p-4 backdrop-blur-sm"
      onClick={onClose}
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="relative w-full max-w-3xl overflow-y-auto rounded-lg bg-white p-8 dark:bg-dark_secondary"
      >
        <button
          onClick={onClose}
          aria-label="Close"
          className="absolute right-4 top-4 text-2xl bg-inherit border-none text-[#0D1B2A] cursor-pointer dark:text-white"
        >
          ×
        </button>

        <h2 className="font-noto mt-5 mb-2 text-center text-3xl font-medium dark:text-white">
          Partner with Rahnaward Academy
        </h2>
        <h3 className="font-noto mb-6 text-center text-2xl font-medium dark:text-white">
          Let’s build the future of education — together.
        </h3>

        <p className=" mx-auto mb-6 max-w-2xl text-center text-sm font-medium dark:text-white/70">
          Are you an organization, institution, or innovator passionate about
          transforming education? Fill out the form below and let’s explore how
          we can grow, innovate, and make a difference together.
        </p>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            alert("Proposal submitted!");
            onClose();
          }}
          className="grid gap-4 sm:grid-cols-2 "
        >
          <FormField placeholder="Your Name" />
          <FormField placeholder="Organization Name" />
          <FormField type="email" placeholder="Email Address" />
          <FormField placeholder="Position in Organization" />
          <div className="col-span-2">
            <FormField placeholder="Website or Portfolio" />
          </div>
          <div className="col-span-2">
            <FormField
              type="textarea"
              placeholder="Tell us why you’d like to partner"
              rows={4}
            />
          </div>

          <div className="col-span-2 mt-4 flex justify-center">
            <ThemedButton addClassName="w-64 !bg-[#CDA84E] border-none font-jakarta hover:!bg-[#D7BA63] !text-black !rounded-none">
              Submit Proposal
            </ThemedButton>
          </div>
        </form>
      </div>
    </div>
  );
}

/* ——— MAIN PAGE ——— */
export default function Partners() {
  const [open, setOpen] = useState(false);

  return (
    <>
      {open && <PartnerModal onClose={() => setOpen(false)} />}

      <SectionX addClassName="overflow-hidden dark:bg-theme_primary">
        <section className="relative mx-auto grid max-w-7xl gap-10 px-6 py-10 lg:py-20 lg:grid-cols-2 lg:items-center lg:gap-20 ">
          <div className="max-w-lg">
            <p className="mb-5 font-noto font-semibold text-[#003164] leading-6 dark:text-white">
              Our Work Is Backed By Those Who{" "}
              <span className="font-extrabold text-[#CDA84E]">
                Lead The Future
              </span>{" "}
              Of Education.
            </p>

            <h1 className="font-noto text-3xl font-semibold text-[#CDA84E] sm:text-4xl md:text-5xl">
              Backed By The{" "}
              <span className="font-extrabold text-[#D7BA63]">Best</span> In The
              Ecosystem
            </h1>

            <p className="mt-5 leading-6 dark:text-white/70">
              We’ve joined hands with industry leaders, visionary educators, and
              forward-thinking organisations that align with our mission to make
              learning smarter and more impactful.
            </p>
          </div>

          <div className="grid grid-cols-2 gap-6">
            <Image
              src="/assets/partners/hero_1.png"
              alt=""
              width={400}
              height={260}
              className="h-auto col-span-2 rounded-lg object-cover shadow-lg w-full md:w-auto"
            />
            <Image
              src="/assets/partners/hero_1.png"
              alt=""
              width={150}
              height={130}
              className="h-auto rounded-lg object-cover shadow-md w-full md:w-auto"
            />
            <Image
              src="/assets/partners/hero_1.png"
              alt=""
              width={150}
              height={130}
              className="h-auto rounded-lg object-cover shadow-md w-full md:w-auto"
            />
          </div>
        </section>

        <section className="bg-[#CDA84E] py-14 text-center">
          <h2 className="font-noto text-4xl font-medium text-[#091B33]">
            Our Partners
          </h2>
          <div className="mx-auto mt-12 flex justify-around flex-wrap max-w-6xl  grid-cols-2 gap-8 p-4 sm:grid-cols-4">
            {partnerLogos.map((src, i) => (
              <Image
                key={i}
                src={src}
                alt="logo"
                width={232}
                height={217}
                className="h-auto w-40 md:w-52"
              />
            ))}
          </div>
        </section>

        {/* ───── News & events ───── */}
        <section className="mx-auto max-w-7xl px-6 py-20">
          <h2 className="mb-12 text-center font-noto text-3xl font-medium text-[#091B33] dark:text-white">
            News And Events
          </h2>
          <div className="grid gap-10 sm:grid-cols-2 lg:grid-cols-3">
            {news.map((n) => (
              <article
                key={n.id}
                className="overflow-hidden rounded-lg bg-white shadow-sm dark:bg-dark_secondary"
              >
                <Image
                  src={n.img}
                  alt=""
                  width={600}
                  height={400}
                  className="h-52 w-full object-cover"
                />
                <div className="space-y-3 p-5">
                  <h3 className=" text-lg font-semibold text-[#091B33] dark:text-white">
                    {n.title}
                  </h3>
                  <p className=" text-sm text-[#091B33] dark:text-white/70">
                    {n.desc}
                  </p>
                </div>
              </article>
            ))}
          </div>
        </section>

        <section className="relative mb-10 rounded-2xl bg-[#091B33] py-20 text-center text-white px-2">
          <h2 className="font-noto text-3xl font-medium sm:text-4xl">
            “Want To Collaborate With Us?”
          </h2>
          <p className="mx-auto mt-6 max-w-2xl  text-[#E0E1DD] text-lg">
            We partner with schools, tech companies, and NGOs to advance AI
            learning.
          </p>

          <ThemedButton
            onClick={() => setOpen(true)}
            addClassName="mx-auto mt-10 w-56 !bg-[#CDA84E] hover:!bg-[#D7BA63] !text-black !rounded-none"
          >
            Become a Partner
          </ThemedButton>

          <Image
            src="/assets/partners/bg_1.png"
            alt="Background"
            width={250}
            height={200}
            className="absolute bottom-0 right-0"
          />
        </section>
      </SectionX>
    </>
  );
}
