import React from "react";

const TermsAndConditions: React.FC = () => {
  return (
    <div className=" text-[#1E293B] dark:text-white min-h-screen dark:bg-theme_primary ">
      <div className="container mx-auto px-6 py-12 max-w-7xl">
        {/* Header */}
        <div className="mb-12">
          <h1 className="text-4xl font-bold text-[#1E293B] dark:text-white mb-4 font-noto ">
            Terms and Conditions
          </h1>
          <p className="text-[#475569] text-lg dark:text-theme_secondary">
            Last updated: July 22, 2025
          </p>
        </div>

        {/* Divider */}
        <hr className="border-white/20 mb-12" />

        {/* Two Column Layout */}
        <div className="grid md:grid-cols-2 gap-12">
          {/* Left Column */}
          <div className="space-y-10">
            {/* Acceptance of Terms */}
            <section>
              <h2 className="text-2xl font-bold text-[#1E293B] dark:text-white mb-4">
                Acceptance of Terms
              </h2>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed mb-4">
                By accessing and using our platform, you accept and agree to be
                bound by these Terms and Conditions. If you do not agree to
                abide by the above, please do not use this service.
              </p>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed">
                These terms apply to all visitors, users, and others who access
                or use the service, including personalized learning features and
                AI-powered support tools.
              </p>
            </section>

            {/* Account Registration */}
            <section>
              <h2 className="text-2xl font-bold text-[#1E293B] dark:text-white mb-4">
                Account Registration
              </h2>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed mb-4">
                To access certain features of our platform, you must create an
                account. You are responsible for maintaining the confidentiality
                of your account credentials and for all activities that occur
                under your account.
              </p>

              <h3 className="text-lg font-semibold text-[#1E293B] dark:text-white mb-3">
                Account Requirements
              </h3>
              <ul className="text-[#475569] dark:text-white/70 space-y-2 ml-4">
                <li>
                  • You must provide accurate and complete information during
                  registration
                </li>
                <li>
                  • You must be at least 13 years of age to create an account
                </li>
                <li>• One person or entity may maintain only one account</li>
                <li>
                  • You must notify us immediately of any unauthorized use of
                  your account
                </li>
              </ul>
            </section>

            {/* Course Access and Usage */}
            <section>
              <h2 className="text-2xl font-bold text-[#1E293B] dark:text-white mb-4">
                Course Access and Usage
              </h2>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed mb-4">
                Our platform provides access to curated courses designed to help
                you achieve your learning goals, from mastering AI tools to
                launching your own business. Course access is subject to the
                following terms:
              </p>

              <h3 className="text-lg font-semibold text-[#1E293B] dark:text-white mb-3">
                Usage Rights
              </h3>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed mb-4">
                Upon enrollment and payment, you receive a limited,
                non-exclusive, non-transferable right to access and view the
                course content for personal, non-commercial use only.
              </p>

              <h3 className="text-lg font-semibold text-[#1E293B] dark:text-white mb-3">
                Restrictions
              </h3>
              <ul className="text-[#475569] dark:text-white/70 space-y-2 ml-4">
                <li>
                  • You may not reproduce, distribute, or share course materials
                </li>
                <li>
                  • Reverse engineering or attempting to extract source code is
                  prohibited
                </li>
                <li>
                  • Commercial use of course content without written permission
                  is forbidden
                </li>
              </ul>
            </section>

            {/* Payment Terms */}
            <section>
              <h2 className="text-2xl font-bold text-[#1E293B] dark:text-white mb-4">
                Payment Terms
              </h2>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed mb-4">
                We offer multiple secure payment options for a smooth checkout
                experience. All payments are processed through trusted
                third-party payment processors.
              </p>

              <h3 className="text-lg font-semibold text-[#1E293B] dark:text-white mb-3">
                Payment Processing
              </h3>
              <ul className="text-[#475569] dark:text-white/70 space-y-2 ml-4">
                <li>
                  • All fees are charged in the currency displayed at checkout
                </li>
                <li>• Payment is required before course access is granted</li>
                <li>
                  • We reserve the right to change pricing with 30 days notice
                </li>
                <li>
                  • Failed payments may result in immediate suspension of access
                </li>
              </ul>
            </section>
          </div>

          {/* Right Column */}
          <div className="space-y-10">
            {/* User Conduct */}
            <section>
              <h2 className="text-2xl font-bold text-[#1E293B] dark:text-white mb-4">
                User Conduct
              </h2>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed mb-4">
                You agree to use our platform responsibly and in compliance with
                all applicable laws. We maintain the right to suspend or
                terminate accounts that violate these standards.
              </p>

              <h3 className="text-lg font-semibold text-[#1E293B] dark:text-white mb-3">
                Prohibited Activities
              </h3>
              <ul className="text-[#475569] dark:text-white/70 space-y-2 ml-4">
                <li>
                  • Harassment, abuse, or discrimination against other users
                </li>
                <li>• Uploading malicious code, viruses, or harmful content</li>
                <li>• Attempting to gain unauthorized access to our systems</li>
                <li>
                  • Using the platform for any illegal or unauthorized purpose
                </li>
                <li>• Spamming or sending unsolicited communications</li>
              </ul>
            </section>

            {/* Intellectual Property */}
            <section>
              <h2 className="text-2xl font-bold text-[#1E293B] dark:text-white mb-4">
                Intellectual Property Rights
              </h2>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed mb-4">
                All content, features, and functionality of our platform are
                owned by us or our licensors and are protected by copyright,
                trademark, and other intellectual property laws.
              </p>

              <h3 className="text-lg font-semibold text-[#1E293B] dark:text-white mb-3">
                Content Ownership
              </h3>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed mb-4">
                Course materials, AI-powered features, user interface design,
                and all associated intellectual property remain our exclusive
                property. Users retain rights to their own submitted content
                while granting us necessary licenses to provide the service.
              </p>

              <h3 className="text-lg font-semibold text-[#1E293B] dark:text-white mb-3">
                User-Generated Content
              </h3>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed">
                By submitting content to our platform, you grant us a worldwide,
                non-exclusive, royalty-free license to use, modify, and display
                such content in connection with providing our services.
              </p>
            </section>

            {/* Refund Policy */}
            <section>
              <h2 className="text-2xl font-bold text-[#1E293B] dark:text-white mb-4">
                Refund Policy
              </h2>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed mb-4">
                We want you to be satisfied with your learning experience. Our
                refund policy is designed to be fair while protecting the
                integrity of our educational content.
              </p>

              <h3 className="text-lg font-semibold text-[#1E293B] dark:text-white mb-3">
                Refund Eligibility
              </h3>
              <ul className="text-[#475569] dark:text-white/70 space-y-2 ml-4">
                <li>
                  • Refund requests must be made within 14 days of purchase
                </li>
                <li>
                  • Course completion must be less than 30% to qualify for
                  refund
                </li>
                <li>
                  • Digital downloads and completed courses are non-refundable
                </li>
                <li>
                  • Refunds are processed to the original payment method within
                  5-10 business days
                </li>
              </ul>
            </section>

            {/* Limitation of Liability */}
            <section>
              <h2 className="text-2xl font-bold text-[#1E293B] dark:text-white mb-4">
                Limitation of Liability
              </h2>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed mb-4">
                Our platform and services are provided &quot;as is&quot; without
                warranties of any kind. We strive to maintain high-quality
                service but cannot guarantee uninterrupted access or error-free
                operation.
              </p>

              <h3 className="text-lg font-semibold text-[#1E293B] dark:text-white mb-3">
                Service Availability
              </h3>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed mb-4">
                While we aim for 99.9% uptime, we do not guarantee that our
                service will be uninterrupted or error-free. Scheduled
                maintenance and unexpected technical issues may temporarily
                affect availability.
              </p>

              <p className="text-[#475569] dark:text-white/70 leading-relaxed">
                In no event shall we be liable for any indirect, incidental,
                special, consequential, or punitive damages, including loss of
                profits, data, or business opportunities.
              </p>
            </section>

            {/* Changes to Terms */}
            <section>
              <h2 className="text-2xl font-bold text-[#1E293B] dark:text-white mb-4">
                Changes to These Terms
              </h2>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed mb-4">
                We reserve the right to modify these Terms and Conditions at any
                time. Changes will be effective immediately upon posting on our
                platform.
              </p>
              <p className="text-[#475569] dark:text-white/70 leading-relaxed">
                Your continued use of our platform after changes are posted
                constitutes acceptance of the revised terms. We encourage you to
                review these terms periodically.
              </p>
            </section>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-16 pt-8 border-t border-[#E2E8F0]">
          <p className="text-[#475569] dark:text-white/70 text-center">
            If you have questions about these Terms and Conditions, please
            contact our support team.
          </p>
        </div>
      </div>
    </div>
  );
};

export default TermsAndConditions;
