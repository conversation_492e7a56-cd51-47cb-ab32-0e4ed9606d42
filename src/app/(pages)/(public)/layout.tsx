"use client";
import React from "react";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { DarkModeProvider } from "@/providers/DarkModeProvider";

export default function AppLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <section>
      <DarkModeProvider>
        <Header />
        {children}
        <Footer />
      </DarkModeProvider>
    </section>
  );
}
