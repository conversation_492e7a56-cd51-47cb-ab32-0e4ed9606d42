"use client";

import Image from "next/image";
import SectionX from "@/components/layout/SectionX";
import { SocialIcon } from "react-social-icons";
import { MailIcon } from "lucide-react";

const whyChoose = [
  {
    icon: "/logos/ranking.png",
    title: "Hands-On Learning",
    desc: "Hands-on learning with live sessions, assignments, and real-world AI use cases. Hands-on learning with live sessions, assignments, and real-world AI use cases.",
    bg: "bg-[#CCE0F6]",
  },
  {
    icon: "/logos/3dcube.png",
    title: "Hands-On Learning",
    desc: "Hands-on learning with live sessions, assignments, and real-world AI use cases. Hands-on learning with live sessions, assignments, and real-world AI use cases.",
    bg: "bg-[#CCF6CC]",
  },
  {
    icon: "/logos/share.png",
    title: "Hands-On Learning",
    desc: "Hands-on learning with live sessions, assignments, and real-world AI use cases. Hands-on learning with live sessions, assignments, and real-world AI use cases.",
    bg: "bg-[#FDFCD6]",
  },
];

const team = Array.from({ length: 3 }).map((_, idx) => ({
  id: idx,
  name: "<PERSON>",
  role: "Senior Mathematics Instructor",
  img: "/assets/about/user.png",
  bio: "With 12+ years of teaching experience, Sir Hassan makes complex mathematical concepts simple and relatable. His mission: to make every student fall in love with numbers.",
}));

export default function About() {
  return (
    <SectionX addClassName="overflow-hidden !px-0 !sm:px-0 !lg:px-0 dark:bg-theme_primary">
      <section
        className="mx-auto grid max-w-7xl gap-10 pt-10 lg:grid-cols-2 lg:items-center lg:gap-24 relative px-10 sm:px-6"
        id="about"
      >
        <div>
          <h1 className="mt-1 font-noto text-3xl sm:text-4xl md:text-5xl font-bold  text-[#CDA84E]">
            <span className="font-noto text-xl sm:text-2xl md:text-3xl font-semibold tracking-wide text-[#091B33] dark:text-white">
              About
            </span>
            Empowering Minds <br />
            Shaping Futures
          </h1>

          <Image
            src="/vectors/lines.png"
            alt=""
            width={150}
            height={10}
            className="absolute h-auto -left-0 top-10 hidden lg:block "
          />

          <p className="mt-6 max-w-md  capitalize leading-relaxed dark:text-white/70">
            At Rahnaward Academy, we believe that <b>AI Education</b> should be
            accessible, practical, and empowering. We help you unlock your
            potential through cutting-edge, hands-on learning experiences.
          </p>
        </div>

        <div className="grid grid-cols-2 gap-6">
          <Image
            src="/assets/about/hero_1.png"
            alt=""
            width={500}
            height={240}
            className="col-span-2 h-auto w-full rounded-xl object-cover shadow-lg"
          />
        </div>
      </section>

      <div id="vision" className="pt-16" />

      <section className="relative  overflow-hidden rounded-2xl bg-[#091B33] py-20 text-center text-white mx-4 sm:mx-6 lg:mx-8 ">
        <Image
          src="/assets/about/bg_2.png"
          alt=""
          width={160}
          height={220}
          className="absolute h-auto -left-0 top-0 opacity-20"
        />
        <Image
          src="/assets/about/bg_1.png"
          alt=""
          width={220}
          height={260}
          className="absolute h-auto right-0 bottom-0 opacity-20"
        />
        <h2 className="font-noto text-3xl font-semibold">Our Vision</h2>

        <p className="mx-auto mt-6 max-w-2xl  font-medium text-lg">
          To democratize AI education and empower individuals across the world
          to innovate, create, and lead using the power of artificial
          intelligence.
        </p>
      </section>

      <section className="mx-auto max-w-6xl px-6 py-20 ">
        <h2 className="text-center font-noto text-3xl font-semibold text-[#091B33] dark:text-white">
          Why Choose Rahnaward Academy?
        </h2>

        <div className="mt-12 grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {whyChoose.map((c, i) => (
            <div
              key={i}
              className={`${c.bg} rounded-xl p-6 space-y-4 shadow-sm`}
            >
              <Image src={c.icon} alt="" width={38} height={38} />
              <h3 className=" text-lg font-bold text-[#091B33]">{c.title}</h3>
              <p className=" font-medium text-[#091B33] leading-6">{c.desc}</p>
            </div>
          ))}
        </div>
      </section>

      <section className="grid overflow-hidden bg-[#CDA84E] py-16 lg:grid-cols-2 lg:items-center px-6 sm:px-6 lg:px-10">
        <div className="space-y-6 max-w-5xl">
          <h1 className="font-noto font-bold text-[#091B33] text-3xl sm:text-5xl">
            Our Success Depends On Our Students’ Success
          </h1>
          <p className=" max-w-lg text-[#091B33] leading-loose capitalize">
            At Rahnaward Academy, we believe that <b>AI education</b> should be
            accessible, practical, and empowering. We help you unlock your
            potential through cutting-edge, hands-on learning experiences.
          </p>

          <div className="mt-8 grid grid-cols-3 gap-5 text-center divide-solid divide-y-0 divide-x divide-[#091B33]/30 max-w-2xl">
            {["Students", "Active Courses", "Teachers"].map((label) => (
              <div key={label} className="px-4">
                <p className=" text-4xl font-bold text-[#091B33]">100+</p>
                <p className=" text-sm font-medium text-[#55585F]">{label}</p>
              </div>
            ))}
          </div>
        </div>
        <div className="mt-10 flex justify-center lg:mt-0">
          <Image
            src="/assets/about/hero_2.png"
            alt=""
            width={500}
            height={420}
            className="rounded-xl w-full md:w-auto h-auto object-cover shadow-lg"
          />
        </div>
      </section>
      <div id="team" className="pt-10" />

      <section className="mx-auto max-w-7xl px-6 pb-20 pt-16">
        <h2 className="text-center font-noto text-3xl font-semibold text-[#091B33] dark:text-white">
          The People Behind Rahnaward Academy
        </h2>
        <p className="mx-auto mt-4 max-w-3xl text-center  text-[#415A77] dark:text-white/70">
          We’re a passionate group of educators, technologists, and AI
          innovators working to make learning smarter, more accessible, and
          future-ready.
        </p>

        <div className="mt-20 grid gap-12 sm:grid-cols-2 lg:grid-cols-3">
          {team.map((m, idx) => (
            <article
              key={m.id}
              className={
                "text-center transition lg:duration-300" +
                (idx === 1 ? " lg:-translate-y-10" : "")
              }
            >
              <Image
                src={m.img}
                alt={m.name}
                width={160}
                height={200}
                className="mx-auto rounded-full object-cover"
              />
              <h3 className="mt-6  text-lg font-semibold text-[#091B33] dark:text-white">
                {m.name}
              </h3>
              <p className="text-sm  font-medium text-[#091B33] dark:text-white">
                {m.role}
              </p>
              <p className=" mt-3 text-sm text-[#091B33] dark:text-white/70">
                {m.bio}
              </p>

              <div className="mt-4 flex items-center justify-center gap-4 ">
                <SocialIcon
                  url="https://www.linkedin.com"
                  bgColor="#091B33"
                  fgColor="#E0E1DD"
                  style={{ height: 24, width: 24 }}
                />
                <MailIcon
                  className="h-6 w-6 cursor-pointer text-[#091B33] dark:text-white"
                  onClick={() =>
                    (window.location.href = "mailto:<EMAIL>")
                  }
                />
              </div>
            </article>
          ))}
        </div>
      </section>
    </SectionX>
  );
}
