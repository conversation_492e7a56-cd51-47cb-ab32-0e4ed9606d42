import type { Metada<PERSON> } from "next";
import "./globals.css";
import { Toaster } from "react-hot-toast";
import { AuthProvider } from "@/providers/AuthProvider";

// ✅ Import font variables from the centralized file
import {
  geistMono,
  geistSans,
  notoSerif,
  plusJakartaSans,
  fontInter,
  fontPoppins,
} from "@/styles/fonts"; // adjust path if needed

export const metadata: Metadata = {
  title: {
    template: "%s | Rahnaward",
    default: "Rahnaward",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${notoSerif.variable} ${plusJakartaSans.variable} ${fontInter.variable} ${fontPoppins.variable} antialiased`}
      >
        <Toaster />
        <AuthProvider>{children}</AuthProvider>
      </body>
    </html>
  );
}
