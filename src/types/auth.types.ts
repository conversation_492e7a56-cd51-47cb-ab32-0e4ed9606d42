import type { JwtPayload } from "jwt-decode";
export interface TokenPair {
  access: string;
  refresh: string;
}

export interface JwtPayloadX extends JwtPayload {
  email: string;
  user_id: number;
  full_name?: string;
  is_registration_complete?: boolean;
}
export interface AuthContextShape {
  user: JwtPayloadX | null;
  updateUser: (u: JwtPayloadX | null) => void;
  logout: () => Promise<void>;
}
