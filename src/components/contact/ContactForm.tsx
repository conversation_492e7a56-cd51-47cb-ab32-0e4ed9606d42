import React from "react";
import FormField from "../common/FormField";

function ContactForm() {
  return (
    <form
      onSubmit={(e) => e.preventDefault()}
      className="flex flex-col gap-6 shadow-lg p-10 bg-white dark:bg-dark_secondary rounded-md"
    >
      <h1 className=" text-[#415A77] font-medium text-3xl dark:text-white">
        Have a Question? Let&apos;s Talk!{" "}
      </h1>
      <FormField placeholder="Your Name" />
      <FormField type="email" placeholder="Email address*" />
      <FormField
        type="textarea"
        placeholder="Write your message here…"
        className="font-jakarta h-40 w-full rounded-md border border-neutral-300 bg-white p-4 text-sm outline-none focus:border-theme_primary"
      />
      <button
        type="submit"
        className=" rounded-md bg-[#CDA84E] border-none py-3 font-medium text-black transition hover:bg-[#CDA84E]/90"
      >
        Send
      </button>
    </form>
  );
}

export default ContactForm;
