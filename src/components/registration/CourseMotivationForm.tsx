"use client";

import { useFormContext } from "react-hook-form";
import Link from "next/link";
import { ROUTES } from "@/constants/routes.constants";
import <PERSON>Field from "../common/FormField";
import { useEffect } from "react";

export default function CourseMotivationForm() {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  useEffect(() => {
    document.getElementById("motivation")?.focus();
  }, []);

  return (
    <div className="flex flex-col gap-4">
      <span className="font-medium text-lg dark:text-white">
        Course Motivation
      </span>

      <FormField
        label="Reason for taking the course"
        name="motivation"
        type="textarea"
        required
        placeholder="Write a few lines about your goal or motivation"
        register={register}
        error={errors.motivation}
        id="motivation"
      />

      <FormField
        label="What do you hope to learn?"
        name="learning_expectations"
        type="textarea"
        required
        placeholder="Share any specific expectations"
        register={register}
        error={errors.learning_expectations}
        rows={2}
      />

      <FormField
        label="Have you taken similar courses before?"
        name="has_taken_similar_courses"
        type="select"
        register={register}
        error={errors.has_taken_similar_courses}
        options={[
          { label: "Select an option", value: "" },
          { label: "Yes", value: "yes" },
          { label: "No", value: "no" },
        ]}
      />
      <FormField
        label={
          <>
            <span className="dark:text-white">
              I agree to Rahnaward Academy&apos;s{" "}
            </span>
            <Link
              href={ROUTES.TERMS_AND_CONDITIONS}
              className="text-blue-600 underline"
            >
              terms & conditions
            </Link>
          </>
        }
        name="agree_terms"
        type="checkbox"
        register={register}
        error={errors.agree_terms}
      />
    </div>
  );
}
