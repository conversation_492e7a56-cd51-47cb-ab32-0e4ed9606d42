import { RegistrationFormData } from "@/app/(pages)/(auth)/registration/page";
import React from "react";
import { FormProvider, UseFormReturn } from "react-hook-form";
import CourseMotivationForm from "./CourseMotivationForm";
import EducationDetailsForm from "./EducationDetailsForm";
import PersonalInformationForm from "./PersonalInformationForm";
import StepIndicators from "./StepIndicators";
import MotionWrapper from "../common/MotionWrapper";

type Props = {
  methods: UseFormReturn<RegistrationFormData>;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  handleNext: () => void;
  isSubmitting?: boolean;
};

const RegistrationForm = ({
  currentStep,
  setCurrentStep,
  methods,
  handleNext,
  isSubmitting = false,
}: Props) => {
  /* ----------------------------------- UI ----------------------------------- */
  return (
    <div>
      <div className="mx-auto flex justify-center flex-col items-center text-theme_primary dark:text-white mt-5 text-center px-5">
        <h1 className="font-bold text-4xl md:text-5xl">Register Now!</h1>
        <p className="mt-3 text-sm md:text-base dark:text-white/70">
          Join Rahnaward Academy and start your learning journey.
        </p>
      </div>
      <div className="flex justify-center items-center mt-5 md:hidden">
        <StepIndicators currentStep={currentStep} />
      </div>
      <FormProvider {...methods}>
        <form className="mt-8  md:max-w-2xl lg:max-w-3xl mx-auto z-50 px-5">
          {currentStep === 1 && (
            <MotionWrapper>
              <PersonalInformationForm />
            </MotionWrapper>
          )}
          {currentStep === 2 && (
            <MotionWrapper>
              <EducationDetailsForm />
            </MotionWrapper>
          )}
          {currentStep === 3 && (
            <MotionWrapper>
              <CourseMotivationForm />
            </MotionWrapper>
          )}
          <MotionWrapper>
            <div className="flex justify-center items-center mt-8 gap-5">
              <button
                type="button"
                className={`w-1/2 md:hidden text-base font-jakarta outline-none  py-2 px-6 rounded-md hover:bg-white transition-all bg-transparent border border-theme_primary text-theme_primary hover:!text-theme_primary/90  ${
                  currentStep === 1
                    ? "cursor-not-allowed opacity-50"
                    : "cursor-pointer"
                }
                dark:border-white/70 dark:text-white/70
                `}
                onClick={() => setCurrentStep(currentStep - 1)}
                disabled={currentStep === 1}
              >
                Back
              </button>
              <button
                type="button"
                className="w-1/2 border-none font-jakarta text-base hover:opacity-80 transition-all cursor-pointer mx-auto rounded-md z-50 bg-theme_primary px-6 py-2 text-white font-medium dark:bg-theme_secondary dark:text-theme_primary dark:hover:!bg-theme_secondary/70"
                onClick={handleNext}
                disabled={isSubmitting}
              >
                {isSubmitting
                  ? "Submitting..."
                  : currentStep === 3
                    ? "Submit"
                    : "Next"}
              </button>
            </div>
          </MotionWrapper>
        </form>
      </FormProvider>
    </div>
  );
};

export default RegistrationForm;
