import React from "react";

type Props = {
  currentStep: number;
};

const StepIndicators = ({ currentStep }: Props) => {
  return (
    <div className="flex items-center gap-4">
      {[1, 2, 3].map((item) => (
        <div
          key={item}
          className={`flex items-center w-12 h-1 rounded-full transition-all ${
            currentStep === item
              ? "bg-theme_primary dark:bg-theme_secondary"
              : "bg-gray-400"
          }`}
        />
      ))}
    </div>
  );
};

export default StepIndicators;
