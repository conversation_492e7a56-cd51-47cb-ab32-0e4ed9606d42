"use client";

import { useFormContext } from "react-hook-form";
import <PERSON><PERSON>ield from "../common/FormField";
import { useEffect } from "react";

export default function EducationDetailsForm() {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  useEffect(() => {
    document.getElementById("basic_education")?.focus();
  }, []);

  return (
    <div className="flex flex-col gap-4">
      <span className="font-medium text-lg dark:text-white">
        Educational Background
      </span>

      <FormField
        label="Basic Education"
        name="basic_education"
        placeholder="Enter your basic education e.g. Matric, O-Level"
        required
        register={register}
        error={errors.basic_education}
        id="basic_education"
      />

      <FormField
        label="Last Degree Completed"
        name="last_degree"
        placeholder="Enter your last degree"
        required
        register={register}
        error={errors.last_degree}
      />

      <FormField
        label="Field of Study"
        name="field_of_study"
        placeholder="Enter your major/field"
        required
        register={register}
        error={errors.field_of_study}
      />

      <FormField
        label="Institution Name"
        name="institution_name"
        placeholder="Where you studied last"
        required
        register={register}
        error={errors.institution_name}
      />
    </div>
  );
}
