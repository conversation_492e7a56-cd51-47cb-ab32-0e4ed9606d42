"use client";

import { useFormContext } from "react-hook-form";
import <PERSON><PERSON><PERSON> from "@/components/common/FormField"; // Adjust path as needed
import { useEffect } from "react";

export default function PersonalInformationForm() {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  useEffect(() => {
    document.getElementById("full_name")?.focus();
  }, []);

  return (
    <div className="flex flex-col gap-4">
      <span className="font-medium text-lg dark:text-white">
        Personal Information
      </span>

      <FormField
        label="Full Name"
        name="full_name"
        type="text"
        required
        placeholder="Enter your full name"
        register={register}
        error={errors.full_name}
        id="full_name"
      />

      {/* <FormField
        label="Email Address"
        name="email"
        type="email"
        required
        placeholder="Enter your email"
        register={register}
        error={errors.email}
      /> */}

      <FormField
        label="Date of Birth"
        name="date_of_birth"
        type="date"
        required
        register={register}
        error={errors.date_of_birth}
      />

      <FormField
        label="CNIC / ID"
        name="cnic_number"
        type="text"
        placeholder="Enter CNIC / ID"
        register={register}
        error={errors.cnic_number}
      />

      <FormField
        label="City / Region"
        name="city"
        type="text"
        required
        placeholder="Enter your city or region"
        register={register}
        error={errors.city}
      />
    </div>
  );
}
