"use client";

import Link from "next/link";
import Logo from "../base/Logo";
import Title from "../base/Title";
import StepIndicators from "./StepIndicators";

type Props = {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  onNext?: () => void;
};

function RegistrationHeader({ currentStep, setCurrentStep, onNext }: Props) {
  const commonClassnames =
    " rounded-full py-2 text-xs font-medium transition-all text-theme_primary px-8 ";
  return (
    <header className="sticky top-0 z-50 w-full  bg-transparent h-header dark:bg-dark_header">
      <nav className="mx-auto flex max-w-[1400px] items-center justify-between px-4 py-4 md:px-6 lg:px-8">
        <Link
          href="/"
          className="flex items-center gap-2 whitespace-nowrap text-neutral-900"
        >
          <Logo />
          <Title />
        </Link>
        <div className="hidden md:block">
          <StepIndicators currentStep={currentStep} />
        </div>
        <div className=" items-center gap-3 hidden md:flex">
          <button
            className={`bg-transparent border outline-none border-theme_primary text-theme_primary dark:border-white/70 dark:text-white/70 ${commonClassnames} ${
              currentStep === 1
                ? "cursor-not-allowed opacity-50"
                : "cursor-pointer"
            }`}
            onClick={() => {
              setCurrentStep(currentStep - 1);
            }}
            disabled={currentStep === 1}
          >
            Back
          </button>
          <button
            className={`bg-theme_primary border-none dark:bg-theme_secondary dark:text-theme_primary dark:hover:!bg-theme_secondary/70 text-white hover:!bg-theme_primary/90 ${commonClassnames} ${
              currentStep === 3
                ? "cursor-not-allowed opacity-50"
                : "cursor-pointer"
            }`}
            onClick={() => {
              onNext?.();
            }}
            disabled={currentStep === 3}
          >
            Next
          </button>
        </div>{" "}
      </nav>
    </header>
  );
}

export default RegistrationHeader;
