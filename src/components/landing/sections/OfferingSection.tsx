import ThemedButton from "@/components/common/ThemedButton";
import SectionX from "@/components/layout/SectionX";
import Image from "next/image";
import React from "react";

function OfferingSection() {
  return (
    <SectionX addClassName="overflow-hidden bg-theme_primary py-16 text-white dark:bg-dark_header ">
      <div className="flex flex-row items-center justify-around">
        <div className="">
          <h2 className="text-5xl font-semibold py-16 ">
            What we are offering?
          </h2>
          <h1 className="font-noto py-10 font-semibold text-5xl 2xl:text-6xl space-y-4 max-w-xl">
            <p>Learn how to use AI</p>
            <p>Effectively</p>
          </h1>
          <p className="text-lg text-[#E0E1DD] pb-10 max-w-lg">
            Master powerful AI tools like ChatGPT, Midjourney, and more — and
            apply them to everyday tasks, learning, and creativity.
          </p>
          <div className="pb-36">
            <ThemedButton addClassName="bg-[#091B33] dark:bg-theme_secondary dark:text-theme_primary border-none font-jakarta !text-base h-16 w-44">
              Start Learning AI
            </ThemedButton>
          </div>
        </div>
        <div className="relative">
          <Image
            src="/assets/home/<USER>"
            alt="Offering Section Image"
            width={1300}
            height={700}
            className="h-auto md:max-w-[70dvw] lg:max-w-[45dvw] xl:max-w-[45dvw] 2xl:max-w-[30dvw]  rounded-[12px] max-lg:hidden blur-sm"
          />
          <Image
            src="/assets/home/<USER>"
            alt="Offering Section Image"
            width={1920}
            height={1080}
            className="h-auto absolute left-12 -bottom-12 md:max-w-[70dvw] lg:max-w-[45dvw] xl:max-w-[45dvw] 2xl:max-w-[30dvw]  rounded-[12px] max-lg:hidden"
          />
        </div>
      </div>
    </SectionX>
  );
}

export default OfferingSection;
