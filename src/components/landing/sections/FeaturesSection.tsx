import React from "react";
import FeatureCard from "../FeatureCard";

function FeaturesSection() {
  return (
    <div className="flex flex-col  px-4 sm:px-6 lg:px-8 py-20">
      <span className="flex flex-col items-center justify-center  !font-semibold  sm:!text-4xl md:!text-5xl !text-3xl text-[#1B263B] dark:text-white">
        <p>Your learning</p>
        <p>
          journey, <span className="text-theme_secondary"> Simplified </span>
        </p>
      </span>
      <span className="flex flex-col items-center justify-center text-center  md:!text-2xl text-xl text-[#1B263B] dark:text-white/70 mt-4">
        <p>From recommendations to tracking, we have designed features for</p>
        <p>effective learning</p>
      </span>
      <div className="mt-12 flex flex-row gap-8 items-center justify-center flex-wrap mx-auto px-2">
        <FeatureCard
          title="GPT Assistance for Students"
          description="Instant AI help for doubts, summaries, and study guidance anytime you need it"
        />
        <FeatureCard
          title="Tailored Courses"
          description="Personalized courses based on your goals, level, and learning style"
          imageUrl="/assets/home/<USER>"
        />
        <FeatureCard
          title="Course Progress"
          description="See your course progress and overall learning journey in one clear view."
        />
      </div>
    </div>
  );
}

export default FeaturesSection;
