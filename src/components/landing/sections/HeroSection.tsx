/* ───────────────────────── HeroSection.tsx ───────────────────────── */
"use client";

import Image from "next/image";
import SectionX from "@/components/layout/SectionX";
import ComingSoonCountdown from "../ComingSoonCountdown";
import MotionWrapper from "@/components/common/MotionWrapper";
import { CheckIcon } from "lucide-react";

const FeatureCard = ({
  title,
  description,
}: {
  title: string;
  description: string;
}) => (
  <MotionWrapper>
    <div className="flex flex-col gap-2 w-full rounded-xl h-fit overflow-hidden bg-white/5 px-4 py-3 sm:px-10 sm:py-4 backdrop-blur-xl relative ">
      <div className="bg-theme_secondary absolute h-2/3 w-[3px] top-1/2 translate-y-[-50%] left-0 rounded-md blur-lg" />
      <span className="text-theme_secondary justify-center md:justify-start font-bold text-base md:text-lg flex items-center gap-2">
        <div className="bg-theme_secondary text-black/70 rounded-md p-1 shadow-md">
          <CheckIcon className="hidden md:block" size={14} strokeWidth={3} />
        </div>
        {title}
      </span>
      <span className="text-white text-sm">{description}</span>
    </div>
  </MotionWrapper>
);

export default function HeroSection() {
  return (
    <SectionX addClassName="h-[calc(100vh-90px)] overflow-hidden max-sm:p-0">
      <div className="relative mx-auto flex h-full flex-col overflow-hidden rounded-[32px] max-sm:rounded-none bg-theme_primary pt-8 sm:pt-10 md:pt-12 text-white">
        <Image
          src="/assets/home/<USER>"
          alt="Offering Section Image"
          width={680}
          height={200}
          className="absolute blur-md md:blur-[10px] bottom-0 md:top-1/2 right-0 md:h-auto md:translate-y-[-30%] z-0"
        />
        <Image
          src="/vectors/arrow_vector.png"
          alt=""
          width={72}
          height={72}
          className="absolute h-24 left-[8%] top-4 hidden lg:block xl:left-[12%]"
        />
        <Image
          src="/vectors/star_2.png"
          alt=""
          width={32}
          height={32}
          className="absolute left-[5%] top-1/2 hidden -translate-y-1/2 lg:block animate-pulse"
        />
        <Image
          src="/vectors/star_1.png"
          alt=""
          width={32}
          height={32}
          className="absolute right-[12%] top-[22%] hidden lg:block animate-pulse"
        />

        <div className="mx-auto w-full max-w-3xl px-4 text-center sm:px-6 z-10">
          <h1 className="font-noto font-normal text-2xl sm:text-3xl lg:text-4xl 2xl:text-5xl">
            Best Courses Are Waiting To&nbsp;
            <br className="hidden md:block" />
            Enrich Your Skills
          </h1>
          <p className="mx-auto mt-5 max-sm:mt-10  max-w-md text-xs sm:max-w-lg sm:text-sm  2xl:text-lg text-white/70">
            Provides you with the latest online learning system and material
            that help your knowledge growing.
          </p>

          {/* <div className="mt-6 md:mt-8  max-sm:mt-20">
            <LandingSearch />
          </div> */}
        </div>

        <div className="my-auto flex justify-center px-4 ">
          {/* <Image
            src="/assets/home/<USER>"
            alt="Dashboard Placeholder"
            width={1600}
            height={900}
            priority
            className="max-w-[calc(100dvw-400px)] rounded-[12px] shadow-sm max-lg:max-w-[100dvw]"
          /> */}
          <div className="grid md:grid-cols-2 gap-5 md:gap-10 mt-5 md:mt-0 md:max-w-[calc(100dvw-400px)] justify-between">
            <div
              className="absolute top-1/2 left-[10%] translate-y-[-30%] z-0 w-[200px] h-[200px] sm:w-[280px] sm:h-[280px] md:w-[400px] md:h-[360px] rounded-full opacity-50 pointer-events-none"
              style={{
                background:
                  "radial-gradient(circle, rgba(59,130,246,0.8) 0%, rgba(59,130,246,0) 70%)",
                filter: "blur(100px)",
              }}
            />

            <div className="grid gap-5 justify-center items-start text-center md:text-left">
              <FeatureCard
                title="AI Quiz"
                description="Personalized quizzes powered by AI to help you master every concept."
              />
              <FeatureCard
                title="AI Analytics"
                description="Our AI tracks your progress, highlights weak areas, and gives you insights to improve faster."
              />
              <FeatureCard
                title="Smart Help. Anytime!"
                description="Our AI tutors guide through concepts, answer questions instantly— 24/7."
              />
            </div>
            <ComingSoonCountdown />
          </div>
        </div>
      </div>
    </SectionX>
  );
}
