import Link from "next/link";
import React from "react";
import CourseCard from "./CourseCard";

type Props = {
  title: string;
  description: string;
  imageUrl?: string;
  link?: string;
};

function FeatureCard({ title, description, imageUrl, link }: Props) {
  return (
    <Link href={link ?? "/"}>
      <div className="flex flex-col max-w-[416px] gap-y-2">
        <div className="h-[500px] sm:w-[416px] w-full rounded-[24px] bg-[#091B33] dark:bg-dark_secondary flex items-center justify-center">
          {/* {imageUrl && (
            <Image src={imageUrl} alt={title} width={300} height={350} />
          )} */}
          {imageUrl && (
            <div className="!p-10">
              <CourseCard />
            </div>
          )}
        </div>
        <span className=" text-2xl  font-semibold text-[#1B263B] dark:text-white">
          {title}
        </span>
        <span className=" text-base text-[#415A77] dark:text-white/70">
          {description}
        </span>
      </div>
    </Link>
  );
}

export default FeatureCard;
