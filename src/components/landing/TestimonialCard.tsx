import Image from "next/image";
import React from "react";

type Props = {
  text: string;
  user: {
    name: string;
    designation: string;
    avatar: string;
  };
};

function TestimonialCard({ text, user }: Props) {
  return (
    <div className="flex h-full flex-col gap-6 rounded-xl bg-neutral-100 p-6 shadow-lg hover:scale-105 transition-all duration-300 ease-in-out dark:bg-dark_secondary">
      <div className="font-times font-bold text-7xl text-[#091B33] dark:text-white">
        “
      </div>

      <p className=" flex-1 text-[#1B263B] leading-6 dark:text-white/70">
        {text}
      </p>

      <div className="flex items-center gap-3">
        <Image
          unoptimized
          src={user.avatar ?? "https://placehold.co/32"}
          alt={`${user.name} avatar`}
          width={32}
          height={32}
          className="h-8 w-8 rounded-full object-cover"
        />
        <div>
          <div className="text-[#1B263B] dark:text-theme_secondary">
            {user.name}
          </div>
          <div className="text-xs text-[#415A77] dark:text-white/70">
            {user.designation}
          </div>
        </div>
      </div>
    </div>
  );
}

export default TestimonialCard;
