import {
  FileBadge2,
  Clock,
  MessageSquareMore,
  StarIcon,
  CircleArrowOutUpRight,
  Heart,
} from "lucide-react";
import Image from "next/image";
import React from "react";

function CourseCard() {
  return (
    <div className="flex flex-col bg-white rounded-md dark:bg-theme_primary">
      <Image
        src="/assets/home/<USER>"
        alt="Course card preview"
        className="h-auto"
        width={380}
        height={88}
      />
      <div className="p-4 flex flex-col">
        <span className="font-bold text-lg leading-tight dark:text-white/90">
          Speak with Impact: Mastering Confident and Persuasive Communication
        </span>
        <div className="flex items-center mt-4 gap-3 text-[11px] font-semibold text-gray-700 dark:text-white/60">
          <span className="flex items-center gap-1">
            <FileBadge2 size={14} />
            12 Lessons
          </span>
          <span className="flex items-center gap-1">
            <Clock size={14} />4 Weeks
          </span>
          <span className="flex items-center gap-1">
            <MessageSquareMore size={14} />
            Group Chat
          </span>
        </div>
        <div className="flex items-center mt-2 gap-3 text-[11px] font-semibold text-gray-700 dark:text-white/60">
          <div className="bg-theme_primary text-gray-200 rounded-md py-1 px-4 text-[9px] tracking-wide">
            Best Seller
          </div>
          <span className="flex items-center gap-1">
            4.5
            {[...Array(4)].map((_, index) => (
              <StarIcon key={index} fill="#FFD700" strokeWidth={0} size={14} />
            ))}
            <StarIcon fill="#E0E1DD" strokeWidth={0} size={14} />
          </span>
          <span className="flex items-center gap-1">(10,000 Reviews)</span>
        </div>
        <div className="flex items-center mt-5 justify-between">
          <h3 className="font-bold text-lg text-theme_primary dark:text-white/80">
            $100
          </h3>
          <div className="flex items-center gap-2">
            <button className="bg-theme_primary border-none dark:bg-dark_secondary text-white px-6 py-[10px] rounded-sm text-xs flex items-center gap-2 hover:bg-theme_primary/90 transition-colors">
              Join Our Course <CircleArrowOutUpRight size={14} />
            </button>
            <div className="border border-solid border-theme_primary p-2 rounded-full text-theme_primary dark:text-white hover:bg-theme_primary/10 transition-colors">
              <Heart size={18} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CourseCard;
