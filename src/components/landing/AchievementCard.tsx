type Props = {
  topText: string;
  middleText: string;
  bottomText: string;
  className?: string;
};

export default function AchievementCard({
  topText,
  middleText,
  bottomText,
  className = "",
}: Props) {
  return (
    <div
      className={`
          rounded-2xl bg-[#f8f8f8] dark:bg-dark_secondary  p-6 shadow-lg hover:scale-105 transition-all duration-300 ease-in-out
          flex flex-col justify-between gap-[2px]
          ${className}
        `}
    >
      <h4 className=" font-[600] text-[#1B263B] dark:text-white/70">
        {topText}
      </h4>
      <p className=" mt-4 xl:text-4xl  text-4xl 3xl:text-6xl  font-semibold text-[#091B33] dark:text-white">
        {middleText}
      </p>
      <p className=" mt-2 text-[#1B263B] font-medium dark:text-white/70">
        {bottomText}
      </p>
    </div>
  );
}
