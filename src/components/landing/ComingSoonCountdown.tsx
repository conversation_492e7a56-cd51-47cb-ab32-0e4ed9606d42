"use client";

import React, { useEffect, useState } from "react";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import MotionWrapper from "../common/MotionWrapper";

dayjs.extend(duration);

export default function ComingSoonCountdown() {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  useEffect(() => {
    const targetDate = dayjs(`${dayjs().year()}-08-14T00:00:00`);

    function getTimeLeft() {
      const now = dayjs();
      const diff = targetDate.diff(now);
      const d = dayjs.duration(diff > 0 ? diff : 0);
      return {
        days: d.days(),
        hours: d.hours(),
        minutes: d.minutes(),
        seconds: d.seconds(),
      };
    }

    setTimeLeft(getTimeLeft());

    const timer = setInterval(() => {
      setTimeLeft(getTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <MotionWrapper>
      <div className="relative text-center px-4 py-8 sm:px-6 sm:py-12 z-10">
        {/* Glow Effect */}

        <div className="relative z-10">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-noto font-bold text-white">
            🎉 Coming Soon!
          </h2>
          <p className="mt-3 sm:mt-4 text-sm sm:text-base  text-[#E0E1DD]">
            Stay tuned! We&apos;re launching something exciting.
          </p>
        </div>

        <div className="mt-8 sm:mt-10 w-fit mx-auto flex flex-wrap sm:flex-nowrap items-center justify-center gap-3 sm:gap-4 rounded-xl bg-white/10 px-4 py-3 sm:px-10 sm:py-5 backdrop-blur-md">
          <CountdownItem label="Days" value={timeLeft.days} />
          <CountdownItem label="Hours" value={timeLeft.hours} />
          <CountdownItem label="Minutes" value={timeLeft.minutes} />
          <CountdownItem label="Seconds" value={timeLeft.seconds} />
        </div>
      </div>
    </MotionWrapper>
  );
}

function CountdownItem({ label, value }: { label: string; value: number }) {
  return (
    <div className="flex flex-col items-center justify-center min-w-[60px] sm:min-w-[80px]">
      <div className="text-xl sm:text-2xl md:text-4xl font-bold font-noto text-white">
        {String(value).padStart(2, "0")}
      </div>
      <div className="text-xs sm:text-sm  text-[#E0E1DD] tracking-wide uppercase">
        {label}
      </div>
    </div>
  );
}
