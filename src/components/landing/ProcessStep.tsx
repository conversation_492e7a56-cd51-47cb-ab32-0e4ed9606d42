import React from "react";

type Props = {
  id: number;
  title: string;
  description: string;
};

function ProcessStep({ id, title, description }: Props) {
  return (
    <div className="flex items-start gap-6">
      <div className=" bg-[#091B33] dark:bg-theme_secondary text-white flex h-10 w-10 shrink-0 items-center justify-center rounded-full text-2xl font-semibold">
        {id}
      </div>

      <div>
        <h3 className="mb-5 text-3xl font-semibold dark:text-white">{title}</h3>
        <p className="text-lg leading-relaxed text-[#1B263B] dark:text-white/70">
          {description}
        </p>
      </div>
    </div>
  );
}

export default ProcessStep;
