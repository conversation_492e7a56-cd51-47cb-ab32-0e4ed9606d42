import Image from "next/image";
import React from "react";

function LandingStats() {
  return (
    <div className="mt-5 flex flex-col items-center gap-3">
      <span className="font-inter text-xs  text-[#E0E1DD] ">
        1000&nbsp;+&nbsp;Educated&nbsp;Students
      </span>
      <div className="flex -space-x-1">
        {Array.from({ length: 10 }).map((_, idx) => (
          <Image
            key={idx}
            unoptimized
            src="https://placehold.co/20"
            alt="Student Avatar"
            width={20}
            height={20}
            className="rounded-full"
          />
        ))}
      </div>
    </div>
  );
}

export default LandingStats;
