"use client";

import { ROUTES } from "@/constants/routes.constants";
import Image from "next/image";
import Link from "next/link";
import { SocialIcon } from "react-social-icons";

export default function Footer() {
  const LinksList1 = [
    { href: ROUTES.ABOUT, label: "About Academy" },
    { href: ROUTES.ABOUT + "#vision", label: "Our Mission & Vision" },
    { href: "/", label: "Careers" },
    { href: ROUTES.ABOUT + "#team", label: "Our Team" },
  ];

  const LinksList2 = [
    { href: "#", label: "All Courses" },
    { href: "#", label: "Signature Programs" },
    { href: "#", label: "Short Courses" },
    { href: "#", label: "Skill Paths" },
    { href: "#", label: "Certifications" },
  ];

  const LinksList3 = [
    { href: "#", label: "Help Center" },
    { href: ROUTES.CONTACT, label: "Contact Us" },
    { href: "#", label: "FAQs" },
  ];

  const renderLinks = (
    title: string,
    links: { href: string; label: string }[]
  ) => (
    <div className="space-y-4">
      <h4 className="font-inter mb-4 font-medium text-lg">{title}</h4>
      <ul className="space-y-2 font-inter">
        {links.map((link) => (
          <li key={link.label}>
            <Link
              href={link.href}
              className="hover:text-theme_secondary transition-colors duration-200 text-gray-200"
            >
              {link.label}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );

  return (
    <footer className="bg-theme_primary text-white dark:bg-dark_header">
      <div className="mx-auto max-w-7xl px-4 pt-16 pb-10 sm:px-6 lg:px-8">
        <div className="grid gap-12 md:grid-cols-5">
          <div className="md:col-span-2">
            <div className="flex items-center gap-1">
              <Image
                src="/logos/logo_new.png"
                alt="Rahnaward logo"
                width={50}
                height={50}
              />
              <span className="font-poppins font-bold text-3xl">
                Rahnaward Academy
              </span>
            </div>

            <p className="mt-4  font-inter">
              Our experts can provide valuable courses and assist you in
              identifying real world problems.
            </p>

            <div className="mt-6 flex gap-4">
              <SocialIcon
                url="https://www.instagram.com/"
                fgColor="#000"
                bgColor="#FFFFFF"
                style={{ height: 32, width: 32 }}
              />
              <SocialIcon
                url="https://www.facebook.com/"
                fgColor="#000"
                bgColor="#FFFFFF"
                style={{ height: 32, width: 32 }}
              />
              <SocialIcon
                url="https://www.youtube.com/"
                fgColor="#000"
                bgColor="#FFFFFF"
                style={{ height: 32, width: 32 }}
              />
            </div>
          </div>
          {renderLinks("About Us", LinksList1)}
          {renderLinks("Courses", LinksList2)}
          {renderLinks("Support", LinksList3)}
        </div>

        <div className="mt-12 border-t border-solid border-0 border-white/20 pt-6">
          <div className="font-inter flex flex-col items-center justify-between gap-4 sm:flex-row">
            <p>©2025 Rahnaward Academy. All rights reserved.</p>
            <div className="flex gap-4">
              <Link
                href={ROUTES.PRIVACY_POLICY}
                className="hover:text-theme_secondary transition-colors duration-200"
              >
                Privacy Policy
              </Link>
              <div className="border-l border-0 border-solid border-white/20 h-5" />
              <Link
                href={ROUTES.TERMS_AND_CONDITIONS}
                className="hover:text-theme_secondary transition-colors duration-200"
              >
                Terms &amp; Conditions
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
