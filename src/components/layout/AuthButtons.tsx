import { ROUTES } from "@/constants/routes.constants";
import Link from "next/link";
import React from "react";

function AuthButtons() {
  const commonClassnames =
    " rounded-full py-2 text-xs font-medium transition-all text-theme_primary";
  return (
    <div className="flex items-center gap-3">
      <Link
        href={ROUTES.SIGN_UP}
        className={`hidden px-6 sm:inline-block border border-solid dark:bg-theme_primary dark:border-white/80 dark:text-white/80 dark:hover:border-white dark:hover:text-white border-theme_primary bg-white hover:bg-theme_primary/10 ${commonClassnames}`}
      >
        Register
      </Link>
      <Link
        href={ROUTES.SIGN_IN}
        className={`bg-theme_secondary px-4 hover:!bg-theme_secondary/90 ${commonClassnames}`}
      >
        Login
      </Link>
    </div>
  );
}

export default AuthButtons;
