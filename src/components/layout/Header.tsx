"use client";

import Link from "next/link";
import Logo from "../base/Logo";
import Title from "../base/Title";
import DesktopNavBar from "./DesktopNavBar";
import AuthButtons from "./AuthButtons";
import UserMenu from "./UserMenu";
import { useAuth } from "@/providers/AuthProvider";
import { MoonStarIcon, Sun } from "lucide-react";
import { useDarkMode } from "@/providers/DarkModeProvider";

function Header() {
  const { user } = useAuth();
  const { isDarkMode, toggleDarkMode } = useDarkMode();

  return (
    <header className="sticky top-0 z-50 w-full dark:bg-dark_header transition-all bg-white h-header">
      <nav className="mx-auto flex max-w-7xl items-center justify-between px-4 py-4 md:px-6 lg:px-8">
        <Link
          href="/"
          className="flex items-center gap-2 whitespace-nowrap text-neutral-900"
        >
          <Logo />
          <Title />
        </Link>

        <DesktopNavBar />

        <div className="flex items-center">
          {user ? <UserMenu /> : <AuthButtons />}
          <button
            className="ml-4 text-gray-500 dark:text-gray-300 bg-inherit border-none"
            onClick={toggleDarkMode}
            aria-label="Toggle Dark Mode"
            title={isDarkMode ? "Switch to Light Mode" : "Switch to Dark Mode"}
          >
            {isDarkMode ? (
              <MoonStarIcon className="h-6 w-6" />
            ) : (
              <Sun className="h-6 w-6 text-yellow-500" />
            )}
          </button>
        </div>
      </nav>
    </header>
  );
}

export default Header;
