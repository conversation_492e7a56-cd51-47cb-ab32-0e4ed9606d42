"use client";

import { useState } from "react";
import { ChevronDown, LogOut, User } from "lucide-react";
import { useAuth } from "@/providers/AuthProvider";
import { logout } from "@/services/auth";
import { ROUTES } from "@/constants/routes.constants";

export default function UserMenu() {
  const { user } = useAuth();
  const [open, setOpen] = useState(false);

  if (!user) return null;

  return (
    <div className=" relative">
      <button
        onClick={() => setOpen((v) => !v)}
        className="flex items-center gap-2 rounded-md px-3 py-1.5 hover:bg-neutral-100 cursor-pointer dark:bg-theme_primary dark:border-none dark:text-white"
      >
        <User size={16} />
        <span className="hidden sm:inline-block font-inter text-sm">
          {user.full_name ?? user.email}
        </span>
        <ChevronDown size={16} />
      </button>

      {open && (
        <div
          className="absolute right-0 mt-2 w-56 rounded-md border bg-white shadow-lg dark:bg-theme_primary"
          onMouseLeave={() => setOpen(false)}
        >
          <div className="px-4 py-3">
            <p className="font-semibold dark:text-white text-sm">
              {user.full_name ?? "—"}
            </p>
            <p className="text-xs  text-neutral-600 dark:text-white/70 truncate">
              {user.email}
            </p>
          </div>
          <button
            className="flex w-full items-center dark:border-white/20 dark:border-0 dark:border-t dark:text-white/70 dark:hover:text-white gap-2 px-4 py-2 text-sm bg-theme_primary/20 text-black cursor-pointer hover:bg-theme_primary/80 hover:text-white"
            onClick={async () => {
              await logout();
              window.location.href = ROUTES.SIGN_IN;
            }}
          >
            <LogOut size={16} /> Sign out
          </button>
        </div>
      )}
    </div>
  );
}
