"use client";

import { NAV_LINKS } from "@/constants/routes.constants";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";

function DesktopNavBar() {
  const path = usePathname();

  const isActive = (href: string) => {
    return path === href;
  };

  return (
    <ul className="hidden md:flex gap-5 lg:gap-8 bg-theme_primary py-[18px] px-6 rounded-full">
      {NAV_LINKS.map(({ href, label }) => (
        <li key={href}>
          <Link
            href={href}
            className={` text-white py-2 px-3 rounded-full transition-all ${
              isActive(href)
                ? "bg-white !text-theme_primary"
                : "hover:bg-white/10 hover:!text-white"
            }`}
          >
            {label}
          </Link>
        </li>
      ))}
    </ul>
  );
}

export default DesktopNavBar;
