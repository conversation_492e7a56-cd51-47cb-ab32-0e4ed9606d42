import React from "react";

type Props = {
  size?: "sm" | "md" | "lg";
};

function Title({ size }: Props) {
  const sizeClasses = {
    sm: "text-sm",
    md: "text-md",
    lg: "text-lg",
  };
  return (
    <span
      className={`${
        sizeClasses[size || "md"]
      } tracking-tighter text-theme_primary uppercase font-times dark:text-white`}
    >
      Rahnaward Academy
    </span>
  );
}

export default Title;
