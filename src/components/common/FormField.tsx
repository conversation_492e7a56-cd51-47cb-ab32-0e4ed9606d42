import React, { ReactNode } from "react";

type FormFieldProps = {
  id?: string;
  label?: string | ReactNode;
  name?: string;
  type?:
    | "text"
    | "email"
    | "date"
    | "number"
    | "checkbox"
    | "textarea"
    | "select"
    | "password";

  placeholder?: string;
  required?: boolean;
  register?: any; // eslint-disable-line
  error?: { message?: string };
  options?: { label: string; value: string | number }[]; // For select
  className?: string;
  rows?: number; // For textarea
};

export default function FormField({
  id,
  label,
  name,
  type = "text",
  placeholder = "",
  required = false,
  register,
  error,
  options = [],
  className = "",
  rows = 4,
}: FormFieldProps) {
  const baseClass =
    "w-full rounded-md border bg-white p-3 text-sm font-inter outline-none text-[#1B263B]";
  const errorClass = error
    ? "border-red-500 focus:border-red-500"
    : "border-gray-200 focus:border-theme_primary";

  const mergedProps = {
    ...register?.(name || ""),
    id: name,
    name,
    className: `${baseClass} ${errorClass} ${className} border border-solid border-gray-400 dark:border-theme_primary dark:bg-theme_primary/70 dark:text-white`,
  };

  return (
    <div className="w-full">
      {/* Label */}
      {type !== "checkbox" && (
        <label
          htmlFor={name}
          className="block text-sm font-medium text-gray-700 mb-1 dark:text-white/70"
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}

      {/* Field */}
      {type === "textarea" ? (
        <textarea
          {...mergedProps}
          placeholder={placeholder}
          rows={rows}
          id={id}
        />
      ) : type === "select" ? (
        <select {...mergedProps} id={id}>
          {options.map((opt) => (
            <option key={opt.value} value={opt.value}>
              {opt.label}
            </option>
          ))}
        </select>
      ) : type === "checkbox" ? (
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            {...register?.(name || "")}
            id={id}
            className="w-4"
          />
          <label htmlFor={name} className="text-sm text-gray-700 flex-grow">
            {label}
          </label>
        </div>
      ) : (
        <input
          {...mergedProps}
          type={type}
          placeholder={placeholder}
          autoComplete={type === "email" ? "email" : "off"}
          id={id}
        />
      )}

      {/* Error message */}
      {error && <p className="mt-1 text-xs text-red-600">{error.message}</p>}
    </div>
  );
}
