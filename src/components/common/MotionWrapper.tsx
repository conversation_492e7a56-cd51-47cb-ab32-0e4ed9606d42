import React from "react";
import { motion } from "framer-motion";

type MotionWrapperProps = {
  children: React.ReactNode;
};

const MotionWrapper = ({ children }: MotionWrapperProps) => {
  return (
    <motion.div
      key="chart-loader"
      variants={{
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0, transition: { duration: 0.25 } },
        exit: { opacity: 0, y: -20, transition: { duration: 0.2 } },
      }}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      {children}
    </motion.div>
  );
};

export default MotionWrapper;
