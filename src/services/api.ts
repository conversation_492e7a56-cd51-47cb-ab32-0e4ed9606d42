import { API_BASE } from "@/constants/api.consants";
import { getValidAccessToken, logout } from "./auth";

type Method = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";

interface Args<T = unknown> {
  url: string;
  method?: Method;
  body?: T;
  headers?: Record<string, string>;
  noAuth?: boolean;
}

export async function request<R = unknown, B = unknown>({
  url,
  method = "GET",
  body,
  headers: extra = {},
  noAuth = false,
}: Args<B>): Promise<R> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    ...extra,
  };

  if (!noAuth) {
    const access = await getValidAccessToken();
    if (access) headers["Authorization"] = `Bearer ${access}`;
  }

  const init: RequestInit = {
    method,
    headers,
    ...(body ? { body: JSON.stringify(body) } : {}),
  };

  let res = await fetch(`${API_BASE}${url}`, init);

  // retry-once
  if (res.status === 401 && !noAuth) {
    try {
      const newAccess = await getValidAccessToken(); // may trigger refresh
      if (newAccess) {
        headers["Authorization"] = `Bearer ${newAccess}`;
        res = await fetch(`${API_BASE}${url}`, init);
      }
    } catch {
      await logout();
      throw new Error("Session expired. Please log in again.");
    }
  }

  if (!res.ok) {
    const detail = (await res.json().catch(() => ({}))).detail;
    throw new Error(detail || res.statusText);
  }
  return res.json() as Promise<R>;
}
