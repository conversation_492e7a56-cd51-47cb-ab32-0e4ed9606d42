import { RegistrationFormData } from "@/app/(pages)/(auth)/registration/page";
import { ACCESS_COOKIE, API_ROUTES } from "@/constants/api.consants";
import { getCookie } from "cookies-next";

export async function saveRegistrationDetails(values: RegistrationFormData) {
  const access = getCookie(ACCESS_COOKIE) as string | undefined;

  const res = await fetch(API_ROUTES.USER.REGISTRATION_DETAILS, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${access}`,
    },
    body: JSON.stringify(values),
  });
  if (!res.ok) throw new Error("Failed to save registration details");
  return res;
}
