"use client";
import { createContext, useContext, useEffect, useState, useMemo } from "react";
import { getValidAccessToken, logout } from "@/services/auth";
import { AuthContextShape, JwtPayloadX } from "@/types/auth.types";
import { jwtDecode } from "jwt-decode";

const AuthContext = createContext<AuthContextShape | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<JwtPayloadX | null>(null);

  /* hydrate once */
  useEffect(() => {
    (async () => {
      try {
        const token = await getValidAccessToken();
        if (token) setUser(jwtDecode<JwtPayloadX>(token));
      } catch {
        setUser(null);
      }
    })();
  }, []);

  const value = useMemo<AuthContextShape>(
    () => ({
      user,
      updateUser: setUser,
      logout: async () => {
        await logout();
        setUser(null);
      },
    }),
    [user]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error("useAuth must be inside <AuthProvider>");
  return ctx;
}
