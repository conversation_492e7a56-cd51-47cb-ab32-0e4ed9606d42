/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,ts,jsx,tsx}"],
  darkMode: "class",
  theme: {
    extend: {
      colors: {
        theme_primary: "#0D1B2A", // from :root
        theme_secondary: "#CDA84E", // from :root
        dark_header: "#102030",
        dark_secondary: "#112334",
      },
      height: {
        header: "90px", // from :root
        headerHeight: "8dvh", // original from config
      },
      margin: {
        headerMargin: "8dvh",
      },
      fontFamily: {
        geist: ["Geist", "sans-serif"],
        geistMono: ["Courier New", "monospace"],
        noto: ["Noto Serif", "serif"],
        jakarta: ["Plus Jakarta Sans", "sans-serif"],
        inter: ["Inter", "sans-serif"],
        poppins: ["Poppins", "sans-serif"],
        times: ["Times New Roman", "serif"],
      },
    },
  },
  corePlugins: {
    preflight: false, // keep false since you're setting font-family manually via @layer base
  },
  plugins: [],
};
