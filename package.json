{"name": "rehnaward", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"autoprefixer": "10.4.14", "postcss": "8.4.27", "tailwindcss": "3.3.3", "@hookform/resolvers": "^5.1.1", "cookies-next": "^6.0.0", "dayjs": "^1.11.13", "framer-motion": "^12.23.6", "jwt-decode": "^4.0.0", "lucide-react": "^0.525.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-social-icons": "^6.24.0", "zod": "^4.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "prettier": "^3.6.2", "typescript": "^5", "prettier-plugin-tailwindcss": "^0.5.4"}}